import warnings

warnings.filterwarnings("ignore")
from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline
import json
import os
import pandas as pd
from sqlalchemy import text
from pyxis.utils.config_loader import ConfigLoader
import warnings

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='database_operations.log',
                    filemode='a')
os.environ['MAIN_KEY'] = '2a36e0e680f441388c4431dab24b11ab'
os.environ['ASSIST_KEY'] = '50a5a01e7ec54576b62f69e96883b33a'
os.environ['ENV'] = 'PROD-AIF'
os.environ['J2C_SALT_ONE'] = '2,3,69,85,100,21,33,76,12,17,63,25,53,69,8,69'
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ[
    'WORK_KEY_CIPHER'] = '20~A2~9CAC7B32C55148437335E1619CB2C3368134E1CABA25C2B6A1EA4CD7BF1BF244~EAA3C29C1F0C77277EF232BD8AEF5D5076CD5A938A2300388040E9D883A12F5A09477B2A20003B86F53BE5AA30D943B2~E693B3E6FCBB6C8F490589C1F8F4A21EAC678DCBF6DA434BD756106D8C68C1CA'
os.environ[
    'STATIC_TOKEN'] = '20~A2~7113DEBEE14EF53C322CE0A864C21099EC2FFC5619A4A791E8F5482BB8C1B60E~A378B72456F3F8F8C34214D6A759089613FF8DBF3CA6A69C733E8C6471D7853A8E33FFB0BE71B9C7D071EC436C2DC96AB7999AE9E65D293F~E5BE4BC473C03D7AD54632C447D7B8BE4453EB8E17AD0935758770358B5AF118'


def load_your_config() -> dict:
    with open(os.path.join("src", "config", f"{os.environ.get('ENV', 'PROD-AIF')}.json"), "r", encoding="utf8") as file:
        return json.load(file)


CONFIG_LOADER = ConfigLoader(load_your_config())
RDB_POOLS = CONFIG_LOADER.get_rdb_pools()5r4
\54re3
]'\'


def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()


startup()
session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
# 取数
session.rollback()  # sql报错之后需要先回滚才能运行
sql = """
select * 
from 
dm_fop_dimension_lv2_tgt_period_filled_t
where
left (target_period,6) < 202407
and del_flag = 'N'
and currency = 'CNY'
and bg_name in ('运营商','政企')
"""
results = session.execute(text(sql))
columns = results.cursor.description
rows = results.fetchall()
results_with_columns = [dict(zip([col[0] for col in columns], row)) for row in rows]
df = pd.DataFrame(results_with_columns)
# # 预测参数
# 每个月都预测到下一年12月-如果有预算的话直接取就好了
max_period_id = df['period_id'].max()
current_year = int(str(max_period_id)[:4])
current_month = int(str(max_period_id)[4:6])
target_month = 12
steps = 12 + (target_month - current_month) + 1
logging.info(f"从当前期到第二年12月的月份个数为：{steps}")
params = {'anomaly_entry_conditions': 'bayes_cycle_test',  # 打开异常检测
          'anomaly_model_name': 'three_sigma',  # 异常检测方法
          'anomaly_kwargs': {'thold': 4,
                             'post_process_method': 'ignore_anomaly_by_month',
                             'ignore_month_list': [11, 12]},  # 异常检测参数
          'reference_detection': True,  # 未知
          'line_trend_thres': 0.8,  # 未知# 线性外推判断# 同正同负比例默认0.7
          'adjust_tag': False,  # 未知# 调整预测结果：保持周期内递增,默认false
          'bayes_p_value': 0.01,  # 变点检测参数
          'is_truncated': True,  # 是否使用变点后的数据# 是否按变点截断数据
          'ignore_last_n': 13,  # 忽略末尾最后几个时点的变点 # 截断数据时忽略最后n个时点中识别出的变点
          'short_ts_threshold': 6,  # 短时序阈值，少于这个阈值，用短时序规则预测
          'fixed_test_size': 6,  # 用于评估模型效果的测试集长度-----ok
          'models': ['prophet', 'auto_ets', 'naive'],  # 选择预测的模型
          'eval_metric': 'mae',  # 评估函数，可选mape、smape、mse-----ok
          'confidence': 0.95,  # 区间预测置信度-----ok
          'ensemble_method': 'weight',  # 模型融合方法，可选best，weight-----ok
          'plot': False,  # 是否要画图，设置为None不画图
          'model_params': {'arima': {'seasonal': True},
                           'auto_ets': {'seasonal_periods': 12,
                                        'params': {'yearly_accumulation': True}},
                           'prophet': {'add_seasonality': {'name': 'yearly',
                                                           'period': 365,
                                                           'fourier_order': 12}}},  # 时序预测模型参数
          'steps': 6,  # 预测步长
          'predict_rule': 'fill_data',  # 规则预测的方法: fill_data/last_value/moving_average-----ok
          'ts_name': 'Data'
          }
import json
import os
import pandas as pd
import numpy as np
import warnings

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.ERROR)
from sqlalchemy import text, Table, MetaData
import datetime
from typing import List, Union, Tuple

dimension_subcategory_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name',
    'currency',
    'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]
dimension_group_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name',
    'currency',
    'dimension_group_code',
    'dimension_group_cn_name',
    'dimension_group_en_name'
]
lv2_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name'
]
# 预测因子
dimension_pred_lst = ['unit_cost', 'unit_price', 'carryover_rate']
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
lv2_withdim_pred_lst = ['mgp_adjust_ratio', 'mca_adjust_ratio']
lv2_nondim_pred_lst = ['mgp_ratio_after', 'mgp_adjust_ratio', 'mca_adjust_ratio', 'equip_rev_cons_after_amt']
# 区间预测
indicators_need_interval_prediction = ['unit_cost', 'unit_price', 'mgp_ratio', 'equip_rev_cons_after_amt']
cols_to_check = lv2_withdim_pred_lst + lv2_nondim_pred_lst
# 检查结果
null_stats = pd.DataFrame({
    '空值数量': df[cols_to_check].isna().sum(),
    '空值占比': df[cols_to_check].isna().mean().round(4) * 100  # 转换为百分比
})
if not null_stats.empty:
    logging.info(
        f"来源:dm_fop_dimension_lv2_filled_period_t表,有空值，{null_stats.to_string()}"
    )


# ytd法
# 有量纲的，需要预测：均本、均价、结转率
def process_row(
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: Union[None, str]
) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    处理单个维度+预测col=预测对象的时序
    处理成Time,Data,UNION_ATTR,没有Miss,有ytd_data
    预测返回5种数据类型
    """
    if df.empty:
        empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
        return empty_df, empty_df, np.array([]), {}, "Missing dimensions"
    # 处理成Time,Data,UNION_ATTR, 没有：Miss,如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    df['UNION_ATTR'] = image_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    # print(data_col,df[['Time', 'Data', 'UNION_ATTR']])
    if data_col + '_ytd_data' in list(df.columns):
        df['ytd_data'] = df[data_col + '_ytd_data']
        _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
        # print(_df)
        # _df.to_csv('nsub_train.csv')
        a, b, c, d, e = rule_profit_pipeline(_df, params)
        # print(a)
        return a, b, c, d, e
    _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    a, b, c, d, e = rule_profit_pipeline(_df, params)
    return a, b, c, d, e


def integrate_results(
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str
) -> pd.DataFrame:
    """
    pred_df的维度抽取自his_df
    整合所有预测结果到pred_df,这里面只有pred的值
    """
    dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}  # his的key:values
    pred_df = his_df[dim].drop_duplicates()
    pred_df = pred_df[pred_df[dim[-1]].apply(lambda x: x == x)]
    result_dfs = []

    for idx, row in pred_df.iterrows():  # 一个维度的预测
        logging.info(f'已添加{len(result_dfs)}个指标,一共有大约1200个指标————————————————————————————')
        dim_key = tuple(row[d] for d in dim)  # pred的key
        # print(dim_key)
        sub_df = dim_dict.get(dim_key, pd.DataFrame())  # 拿着pred的key,找his_df的values
        result_row = row.to_dict()  # 维度 # 当前这一条key-value
        for pred_col in pred_cols:  # 一个指标的预测
            pred, pi_pred, pi_dist, mid_data, msg = process_row(sub_df, period_col=period_col, data_col=pred_col,
                                                                image_name='-'.join(map(str, dim_key)) + pred_col)
            # print('qq',pred)#, pi_pred, pi_dist, mid_data, msg)
            # print(mid_data)
            pi_confidence = mid_data['pi_confidence']
            eval_result = mid_data['eval_result']

            pred = pred.reset_index()  # 把Time从索引转为列名,不能仅重复运行这行，会增加列
            pi_pred = pi_pred.reset_index()  # 把Time从索引转为列名
            pred_ts = pred.merge(
                pi_pred,
                on='Time',
                how='left'
            )  # 点预测和区间预测合并
            # print(pred_ts)#不对
            temp = result_row.copy()  # 维度
            # 需要区间预测
            if pred_col in indicators_need_interval_prediction:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row['Time'],
                            pred_col + '_fcst': time_row['yhat'],
                            pred_col + '_fcst_upper': time_row['yhat_upper'],
                            pred_col + '_fcst_lower': time_row['yhat_lower'],
                            pred_col + '_pi_dist': pi_dist,
                            pred_col + '_fcst_conf': pi_confidence,
                            pred_col + '_fcst_acc': eval_result,  # 均本均价的时候要看这个
                            'msg': msg
                        })
                        result_dfs.append(temp)  # 单指标每个月预测的结果行都加上了
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        pred_col + '_fcst': None,
                        pred_col + '_fcst_upper': None,
                        pred_col + '_fcst_lower': None,
                        pred_col + '_pi_dist': None,
                        pred_col + '_fcst_conf': None,
                        pred_col + '_fcst_acc': None,
                        'msg': None
                    })
                    result_dfs.append(temp)
            else:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row['Time'],
                            pred_col + '_fcst': time_row['yhat'],
                            pred_col + '_pi_dist': pi_dist,
                            'msg': msg
                        })
                        # print('qq',temp,)#time_row[['Time','yhat']])
                        result_dfs.append(temp)  # 单指标每个月预测的结果行都加上了
                        # print('qq',result_dfs)
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        pred_col + '_fcst': None,
                        pred_col + '_pi_dist': None,
                        'msg': None
                    })
                    result_dfs.append(temp)
    main_result = pd.DataFrame(result_dfs)
    return main_result


# df.rename(columns={
#     'mgp_ratio_ytd_data': 'mgp_ratio_after_ytd_data'
# }, inplace=True)
# ytd_data填充空值为 1
cols_endwith_ytd_data = list(df.filter(regex='_ytd_data$').columns)
df[cols_endwith_ytd_data] = df[cols_endwith_ytd_data].fillna(1)

# lv2无量纲的
lv2_nondim_his_df = df[(df['scenarios'] == 'LV2')]
lv2_nondim_pred_df = integrate_results(his_df=lv2_nondim_his_df,
                                       dim=lv2_dim, pred_cols=lv2_nondim_pred_lst, period_col='target_period')
# lv2有量纲的
# lv2_withdim_his_df = df[df['scenarios'].isin(['量纲子类', '量纲分组'])]
# lv2_withdim_pred_df = integrate_results(his_df=lv2_withdim_his_df,
#                                         dim=lv2_dim, pred_cols=lv2_withdim_pred_lst, period_col='target_period')
# 合并以上预测数据到一个表，以下的表只有预测的数据
# 按行拼接，保留所有字段，空值用NaN填充
# df = pd.concat([lv2_nondim_pred_df, lv2_withdim_pred_df], axis=0, ignore_index=True)
df = lv2_withdim_pred_df.groupby([
    'scenarios',
    'bg_code', 'bg_name',
    'target_period',
    'oversea_code', 'oversea_desc',
    'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name'
]).agg({
    pred_col + '_fcst': 'max' for pred_col in lv2_nondim_pred_lst  # 取非空的最大值（即有效值）
}).reset_index()
df.to_csv('lv2_nodim_df.csv')
