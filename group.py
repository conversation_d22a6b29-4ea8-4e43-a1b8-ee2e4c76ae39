import time
import warnings

from pyxis.utils.LOGGER import LOGGER

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.ERROR)
from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline
import json
import os
import pandas as pd
from sqlalchemy import text
from pyxis.utils.config_loader import ConfigLoader
import warnings

warnings.filterwarnings("ignore")
import logging

# logging.basicConfig(level=logging.ERROR)
os.environ['MAIN_KEY'] = '2a36e0e680f441388c4431dab24b11ab'
os.environ['ASSIST_KEY'] = '50a5a01e7ec54576b62f69e96883b33a'
os.environ['ENV'] = 'PROD-AIF'
os.environ['J2C_SALT_ONE'] = '2,3,69,85,100,21,33,76,12,17,63,25,53,69,8,69'
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ[
    'WORK_KEY_CIPHER'] = '20~A2~9CAC7B32C55148437335E1619CB2C3368134E1CABA25C2B6A1EA4CD7BF1BF244~EAA3C29C1F0C77277EF232BD8AEF5D5076CD5A938A2300388040E9D883A12F5A09477B2A20003B86F53BE5AA30D943B2~E693B3E6FCBB6C8F490589C1F8F4A21EAC678DCBF6DA434BD756106D8C68C1CA'
os.environ[
    'STATIC_TOKEN'] = '20~A2~7113DEBEE14EF53C322CE0A864C21099EC2FFC5619A4A791E8F5482BB8C1B60E~A378B72456F3F8F8C34214D6A759089613FF8DBF3CA6A69C733E8C6471D7853A8E33FFB0BE71B9C7D071EC436C2DC96AB7999AE9E65D293F~E5BE4BC473C03D7AD54632C447D7B8BE4453EB8E17AD0935758770358B5AF118'


def load_your_config() -> dict:
    with open(os.path.join("src", "src/config", f"{os.environ.get('ENV', 'PROD-AIF')}.json"), "r", encoding="utf8") as file:
        return json.load(file)


CONFIG_LOADER = ConfigLoader(load_your_config())
RDB_POOLS = CONFIG_LOADER.get_rdb_pools()
LOGGER = CONFIG_LOADER.get_LOGGER()

def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()


startup()
session = next(RDB_POOLS["pg_demo"].get_session_with_commit())
# 取数
session.rollback()  # sql报错之后需要先回滚才能运行
sql = """
select * 
from 
dm_fop_dimension_tgt_period_filled_t
where
left (target_period,6) < 202407
and del_flag = 'N'
and currency = 'CNY'
and bg_name in ('运营商','政企')
"""
results = session.execute(text(sql))
columns = results.cursor.description
rows = results.fetchall()
results_with_columns = [dict(zip([col[0] for col in columns], row)) for row in rows]
df = pd.DataFrame(results_with_columns)
# # 预测参数
# 每个月都预测到下一年12月-如果有预算的话直接取就好了
max_period_id = df['period_id'].max()
current_year = int(str(max_period_id)[:4])
current_month = int(str(max_period_id)[4:6])
target_month = 12
steps = 12 + (target_month - current_month) + 1
logging.info(f"从当前期到第二年12月的月份个数为：{steps}")
params = {'anomaly_entry_conditions': 'bayes_cycle_test',  # 打开异常检测
          'anomaly_model_name': 'three_sigma',  # 异常检测方法
          'anomaly_kwargs': {'thold': 4,
                             'post_process_method': 'ignore_anomaly_by_month',
                             'ignore_month_list': [11, 12]},  # 异常检测参数
          'reference_detection': True,  # 未知
          'line_trend_thres': 0.8,  # 未知# 线性外推判断# 同正同负比例默认0.7
          'adjust_tag': False,  # 未知# 调整预测结果：保持周期内递增,默认false
          'bayes_p_value': 0.01,  # 变点检测参数
          'is_truncated': True,  # 是否使用变点后的数据# 是否按变点截断数据
          'ignore_last_n': 13,  # 忽略末尾最后几个时点的变点 # 截断数据时忽略最后n个时点中识别出的变点
          'short_ts_threshold': 6,  # 短时序阈值，少于这个阈值，用短时序规则预测
          'fixed_test_size': 6,  # 用于评估模型效果的测试集长度-----ok
          'models': ['prophet', 'auto_ets', 'naive'],  # 选择预测的模型
          'eval_metric': 'mae',  # 评估函数，可选mape、smape、mse-----ok
          'confidence': 0.95,  # 区间预测置信度-----ok
          'ensemble_method': 'weight',  # 模型融合方法，可选best，weight-----ok
          'plot': False,  # 是否要画图，设置为None不画图
          'model_params': {'arima': {'seasonal': True},
                           'auto_ets': {'seasonal_periods': 12,
                                        'params': {'yearly_accumulation': True}},
                           'prophet': {'add_seasonality': {'name': 'yearly',
                                                           'period': 365,
                                                           'fourier_order': 12}}},  # 时序预测模型参数
          'steps': 6,  # 预测步长
          'predict_rule': 'fill_data',  # 规则预测的方法: fill_data/last_value/moving_average-----ok
          'ts_name': 'Data'
          }
import json
import os
import pandas as pd
import numpy as np
import warnings

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.ERROR)
from sqlalchemy import text, Table, MetaData
import datetime
from typing import List, Union, Tuple

dimension_subcategory_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name',
    'currency',
    'dimension_subcategory_code',
    'dimension_subcategory_cn_name',
    'dimension_subcategory_en_name',
]
dimension_group_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name',
    'currency',
    'dimension_group_code',
    'dimension_group_cn_name',
    'dimension_group_en_name'
]
lv2_dim = [
    'scenarios',
    'bg_code',
    'bg_name',
    'oversea_code',
    'oversea_desc',
    'lv1_code',
    'lv1_name',
    'lv2_code',
    'lv2_name'
]
# 预测因子
dimension_pred_lst = ['unit_cost', 'unit_price', 'carryover_rate']
nondimension_pred_lst = ['rev_percent', 'mgp_ratio']
lv2_withdim_pred_lst = ['mgp_adjust_ratio', 'mca_adjust_ratio']
lv2_nondim_pred_lst = ['mgp_ratio_after', 'mgp_adjust_ratio', 'mca_adjust_ratio', 'equip_rev_cons_after_amt']
# 区间预测
indicators_need_interval_prediction = ['unit_cost', 'unit_price', 'mgp_ratio', 'equip_rev_cons_after_amt']
cols_to_check = dimension_pred_lst + nondimension_pred_lst
# 检查结果
null_stats = pd.DataFrame({
    '空值数量': df[cols_to_check].isna().sum(),
    '空值占比': df[cols_to_check].isna().mean().round(4) * 100  # 转换为百分比
})
if not null_stats.empty:
    logging.info(
        f"来源:dm_fop_dimension_tgt_period_t表,有空值，{null_stats.to_string()}"
    )


# ytd法
# 有量纲的，需要预测：均本、均价、结转率
def process_row(
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: Union[None, str]
) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    处理单个维度+预测col=预测对象的时序
    处理成Time,Data,UNION_ATTR,没有Miss,有ytd_data
    预测返回5种数据类型
    """
    if df.empty:
        empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
        return empty_df, empty_df, np.array([]), {}, "Missing dimensions"
    # 处理成Time,Data,UNION_ATTR, 没有：Miss,如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    df['UNION_ATTR'] = image_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    # print(data_col,df[['Time', 'Data', 'UNION_ATTR']])
    if data_col + '_ytd_data' in list(df.columns):
        df['ytd_data'] = df[data_col + '_ytd_data']
        _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
        # print(_df)
        # _df.to_csv('nsub_train.csv')
        a, b, c, d, e = rule_profit_pipeline(_df, params)
        # print(a)
        return a, b, c, d, e
    _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    a, b, c, d, e = rule_profit_pipeline(_df, params)
    return a, b, c, d, e


from multiprocessing import Pool
from multiprocessing import Pool
import pandas as pd
from typing import List, Dict, Tuple, Any


def integrate_results1(
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str
) -> pd.DataFrame:
    """
    pred_df的维度抽取自his_df
    整合所有预测结果到pred_df,这里面只有pred的值
    """
    dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}  # his的key:values
    pred_df = his_df[dim].drop_duplicates()
    pred_df = pred_df[pred_df[dim[-1]].apply(lambda x: x == x)]
    result_dfs = []

    for idx, row in pred_df.iterrows():  # 一个维度的预测
        dim_key = tuple(row[d] for d in dim)  # pred的key
        # print(dim_key)
        sub_df = dim_dict.get(dim_key, pd.DataFrame())  # 拿着pred的key,找his_df的values
        result_row = row.to_dict()  # 维度 # 当前这一条key-value
        for pred_col in pred_cols:  # 一个指标的预测
            pred, pi_pred, pi_dist, mid_data, msg = process_row(sub_df, period_col=period_col, data_col=pred_col,
                                                                image_name='-'.join(map(str, dim_key)) + pred_col)
            # print('qq',pred)#, pi_pred, pi_dist, mid_data, msg)
            # print(mid_data)
            pi_confidence = mid_data['pi_confidence']
            eval_result = mid_data['eval_result']

            pred = pred.reset_index()  # 把Time从索引转为列名,不能仅重复运行这行，会增加列
            pi_pred = pi_pred.reset_index()  # 把Time从索引转为列名
            pred_ts = pred.merge(
                pi_pred,
                on='Time',
                how='left'
            )  # 点预测和区间预测合并
            # print(pred_ts)#不对
            temp = result_row.copy()  # 维度
            # 需要区间预测
            if pred_col in indicators_need_interval_prediction:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row['Time'],
                            pred_col + '_fcst': time_row['yhat'],
                            pred_col + '_fcst_upper': time_row['yhat_upper'],
                            pred_col + '_fcst_lower': time_row['yhat_lower'],
                            pred_col + '_pi_dist': pi_dist,
                            pred_col + '_fcst_conf': pi_confidence,
                            pred_col + '_fcst_acc': eval_result,  # 均本均价的时候要看这个
                            'msg': msg
                        })
                        result_dfs.append(temp)  # 单指标每个月预测的结果行都加上了
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        pred_col + '_fcst': None,
                        pred_col + '_fcst_upper': None,
                        pred_col + '_fcst_lower': None,
                        pred_col + '_pi_dist': None,
                        pred_col + '_fcst_conf': None,
                        pred_col + '_fcst_acc': None,
                        'msg': None
                    })
                    result_dfs.append(temp)
            else:
                if not pred_ts.empty:
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row['Time'],
                            pred_col + '_fcst': time_row['yhat'],
                            pred_col + '_pi_dist': pi_dist,
                            'msg': msg
                        })
                        # print('qq',temp,)#time_row[['Time','yhat']])
                        result_dfs.append(temp)  # 单指标每个月预测的结果行都加上了
                        # print('qq',result_dfs)
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        pred_col + '_fcst': None,
                        pred_col + '_pi_dist': None,
                        'msg': None
                    })
                    result_dfs.append(temp)
    main_result = pd.DataFrame(result_dfs)
    return main_result


def process_dimension(
        dim_key: Tuple[Any],
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        sub_df: pd.DataFrame
) -> List[Dict[str, Any]]:
    """
    处理单个维度组合的所有预测列
    """
    LOGGER.info(f"Start processing dimension key: {dim_key}")
    start_time = time.time()

    result_rows = []
    result_row = {d: v for d, v in zip(dim, dim_key)}  # 构建维度字段

    for pred_col in pred_cols:
        LOGGER.debug(f"Processing prediction column: {pred_col} for {dim_key}")
        try:
            # 调用预测函数
            pred, pi_pred, pi_dist, mid_data, msg = process_row(
                sub_df,
                period_col=period_col,
                data_col=pred_col,
                image_name='-'.join(map(str, dim_key)) + pred_col
            )

            pi_confidence = mid_data.get('pi_confidence', None)
            eval_result = mid_data.get('eval_result', None)

            # 重置索引
            pred = pred.reset_index()
            pi_pred = pi_pred.reset_index()

            # 合并点预测和区间预测
            pred_ts = pred.merge(pi_pred, on=period_col, how='left')

            # 处理结果行
            if pred_col in indicators_need_interval_prediction:
                if not pred_ts.empty:
                    rows_added = 0
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row[period_col],
                            f"{pred_col}_fcst": time_row['yhat'],
                            f"{pred_col}_fcst_upper": time_row.get('yhat_upper', None),
                            f"{pred_col}_fcst_lower": time_row.get('yhat_lower', None),
                            f"{pred_col}_pi_dist": pi_dist,
                            f"{pred_col}_fcst_conf": pi_confidence,
                            f"{pred_col}_fcst_acc": eval_result,
                            'msg': msg
                        })
                        result_rows.append(temp)
                        rows_added += 1
                    LOGGER.debug(f"Added {rows_added} rows for {pred_col} with interval prediction")
                else:
                    # 处理空数据情况
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        f"{pred_col}_fcst": None,
                        f"{pred_col}_fcst_upper": None,
                        f"{pred_col}_fcst_lower": None,
                        f"{pred_col}_pi_dist": None,
                        f"{pred_col}_fcst_conf": None,
                        f"{pred_col}_fcst_acc": None,
                        'msg': None
                    })
                    result_rows.append(temp)
                    LOGGER.warning(f"No data for {pred_col} with interval prediction in {dim_key}")
            else:
                if not pred_ts.empty:
                    rows_added = 0
                    for _, time_row in pred_ts.iterrows():
                        temp = result_row.copy()
                        temp.update({
                            period_col: time_row[period_col],
                            f"{pred_col}_fcst": time_row['yhat'],
                            f"{pred_col}_pi_dist": pi_dist,
                            'msg': msg
                        })
                        result_rows.append(temp)
                        rows_added += 1
                    LOGGER.debug(f"Added {rows_added} rows for {pred_col} without interval prediction")
                else:
                    temp = result_row.copy()
                    temp.update({
                        period_col: None,
                        f"{pred_col}_fcst": None,
                        f"{pred_col}_pi_dist": None,
                        'msg': None
                    })
                    result_rows.append(temp)
                    LOGGER.warning(f"No data for {pred_col} without interval prediction in {dim_key}")
        except Exception as e:
            LOGGER.error(f"Error processing {pred_col} for {dim_key}: {str(e)}", exc_info=True)
            # 添加错误标记行
            temp = result_row.copy()
            temp.update({
                period_col: None,
                f"{pred_col}_fcst": None,
                f"{pred_col}_fcst_upper": None,
                f"{pred_col}_fcst_lower": None,
                f"{pred_col}_pi_dist": None,
                f"{pred_col}_fcst_conf": None,
                f"{pred_col}_fcst_acc": None,
                'msg': f"Error: {str(e)}"
            })
            result_rows.append(temp)

    duration = time.time() - start_time
    LOGGER.info(f"Finished processing {dim_key} in {duration:.2f}s. Generated {len(result_rows)} rows")
    return result_rows


def integrate_results(
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str
) -> pd.DataFrame:
    """
    整合所有预测结果到 DataFrame
    """
    LOGGER.info("Starting integration of results")

    # 创建维度分组字典
    LOGGER.debug("Creating dimension dictionary")
    dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}

    # 提取唯一的维度组合并过滤掉 NaN
    LOGGER.debug("Preparing unique dimension combinations")
    pred_df = his_df[dim].drop_duplicates()
    pred_df = pred_df.dropna(subset=[dim[-1]])  # 假设最后一个维度不能为 NaN

    # 记录任务总数
    total_tasks = len(pred_df)
    LOGGER.info(f"Found {total_tasks} unique dimension combinations to process")

    # 准备多进程任务参数
    LOGGER.debug("Preparing multiprocessing tasks")
    tasks = []
    for _, row in pred_df.iterrows():
        dim_key = tuple(row[d] for d in dim)
        sub_df = dim_dict.get(dim_key, pd.DataFrame())
        tasks.append((dim_key, dim, pred_cols, period_col, sub_df))

    # 根据 CPU 核心数设置进程池大小
    processes = cpu_count()
    LOGGER.info(f"Using {processes} processes for parallel execution")

    start_time = time.time()
    with Pool(processes=processes) as pool:
        LOGGER.info("Starting parallel processing")
        results = pool.starmap(process_dimension, tasks)

    duration = time.time() - start_time
    LOGGER.info(f"Parallel processing completed in {duration:.2f}s")

    # 合并所有结果
    LOGGER.debug("Merging results")
    main_result = pd.DataFrame([item for sublist in results for item in sublist])
    LOGGER.info(f"Final result contains {len(main_result)} rows")

    LOGGER.info("Integration completed successfully")
    return main_result


# # 示例调用
# if __name__ == "__main__":
#     # 示例数据
#     his_df = pd.DataFrame({
#         'dim1': ['A', 'A', 'B', 'B'],
#         'dim2': [1, 1, 2, 2],
#         'target': [10, 20, 30, 40]
#     })
#
#     result = integrate_results(
#         his_df=his_df,
#         dim=['dim1', 'dim2'],
#         pred_cols=['target'],
#         period_col='Time'
#     )
#     print(result)


# df.rename(columns={
#     'mgp_ratio_ytd_data': 'mgp_ratio_after_ytd_data'
# }, inplace=True)
# ytd_data填充空值为 1
cols_endwith_ytd_data = list(df.filter(regex='_ytd_data$').columns)
df[cols_endwith_ytd_data] = df[cols_endwith_ytd_data].fillna(1)
# 量纲分组
dimension_group_his_df = df[(df['scenarios'] == '量纲分组') & (df['dimension_group_code'] != 'NODIM')]
dimension_group_pred_df = integrate_results(his_df=dimension_group_his_df,
                                            dim=dimension_group_dim, pred_cols=dimension_pred_lst,
                                            period_col='target_period')
df = dimension_group_pred_df.groupby([
    'scenarios',
    'bg_code', 'bg_name',
    'target_period',
    'oversea_code', 'oversea_desc',
    'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
    'currency',
    'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
]).agg({
    'unit_cost_fcst': 'max',
    'carryover_rate_fcst': 'max',
    'unit_price_fcst': 'max'  # 取非空的最大值（即有效值）
}).reset_index()
df.to_csv('dimension_group_pred_df.csv')

# 保存到数据库
import warnings

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.ERROR)
from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline
import json
import os
import pandas as pd
from sqlalchemy import text
from pyxis.utils.config_loader import ConfigLoader
import warnings

warnings.filterwarnings("ignore")
import logging

logging.basicConfig(level=logging.ERROR)
os.environ['MAIN_KEY'] = '2a36e0e680f441388c4431dab24b11ab'
os.environ['ASSIST_KEY'] = '50a5a01e7ec54576b62f69e96883b33a'
os.environ['ENV'] = 'PROD-AIF'
os.environ['J2C_SALT_ONE'] = '2,3,69,85,100,21,33,76,12,17,63,25,53,69,8,69'
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ[
    'WORK_KEY_CIPHER'] = '20~A2~9CAC7B32C55148437335E1619CB2C3368134E1CABA25C2B6A1EA4CD7BF1BF244~EAA3C29C1F0C77277EF232BD8AEF5D5076CD5A938A2300388040E9D883A12F5A09477B2A20003B86F53BE5AA30D943B2~E693B3E6FCBB6C8F490589C1F8F4A21EAC678DCBF6DA434BD756106D8C68C1CA'
os.environ[
    'STATIC_TOKEN'] = '20~A2~7113DEBEE14EF53C322CE0A864C21099EC2FFC5619A4A791E8F5482BB8C1B60E~A378B72456F3F8F8C34214D6A759089613FF8DBF3CA6A69C733E8C6471D7853A8E33FFB0BE71B9C7D071EC436C2DC96AB7999AE9E65D293F~E5BE4BC473C03D7AD54632C447D7B8BE4453EB8E17AD0935758770358B5AF118'


def load_your_config() -> dict:
    with open(os.path.join("src", "src/config", f"{os.environ.get('ENV', 'PROD-AIF')}.json"), "r", encoding="utf8") as file:
        return json.load(file)


CONFIG_LOADER = ConfigLoader(load_your_config())
RDB_POOLS = CONFIG_LOADER.get_rdb_pools()


def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()


startup()
session = next(RDB_POOLS["pg_demo"].get_session_with_commit())


def save_to_db(df):
    df.rename(columns=lambda col: col.replace('ratio_', 'ratio_before_')
    if col.startswith('mgp_ratio_') else col, inplace=True)
    df.rename(columns=lambda col: col.replace('carryover_rate', 'carryover_ratio')
    if col.startswith('carryover_rate') else col, inplace=True)
    df.rename(columns=lambda col: col.replace('mgp_ratio', 'mgp_rate'), inplace=True)

    cols_should_be_saved = ['period_id', 'time_window_code', 'fcst_type', 'scenarios',
                            'bg_code', 'bg_name',
                            'oversea_code', 'oversea_desc',
                            'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name',
                            'currency',
                            'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
                            'dimension_subcategory_code', 'dimension_subcategory_cn_name',
                            'dimension_subcategory_en_name',
                            'target_period',
                            'rev_percent_fcst', 'unit_price_fcst_conf',
                            'unit_price_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_lower', 'unit_cost_fcst_conf',
                            'unit_cost_fcst', 'unit_cost_fcst_upper', 'unit_cost_fcst_lower',
                            'mgp_rate_before_fcst_conf',
                            'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper', 'mgp_rate_before_fcst_lower',
                            'unit_price_fcst_acc', 'unit_cost_fcst_acc',
                            'carryover_ratio_fcst']
    intersection = list(set(cols_should_be_saved) & set(df.columns))
    df = df[intersection]
    df['period_id'] = 202506
    df['fcst_type'] = 'YTD法'
    df['target_period'] = pd.to_datetime(df['target_period']).dt.strftime("%Y%m")
    df['remark'] = '06201424'  # 备注，默认为空字符串
    # 添加 created_by，默认为 'm00842394'
    # df['created_by'] = 00842394
    # 添加 creation_date，默认为当前时间
    # from datetime import datetime
    # current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # df['creation_date'] = current_time
    # # 添加 last_updated_by，默认为 'm00842394'
    # df['last_updated_by'] = 'm00842394'
    # 添加 last_update_date，默认为当前时间
    # df['last_update_date'] = current_time
    # # 添加 del_flag，默认为 'N'
    df['del_flag'] = 'N'
    # 确保其他列的数据类型正确
    # 显式转换数值列，替换 NaN 为 None
    for col in ['rev_percent_fcst', 'unit_price_fcst_conf', 'unit_price_fcst', 'unit_price_fcst_upper',
                'unit_price_fcst_lower', 'unit_cost_fcst_conf', 'unit_cost_fcst', 'unit_cost_fcst_upper',
                'unit_cost_fcst_lower',
                'mgp_rate_before_fcst_conf', 'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper',
                'mgp_rate_before_fcst_lower',
                'unit_price_fcst_acc', 'unit_cost_fcst_acc',
                'carryover_ratio_fcst']:
        if col in df.columns:
            # 替换非数值为NaN
            df[col] = pd.to_numeric(df[col], errors='coerce')
            df[col] = df[col].astype(float).replace({np.nan: None})
    # print(df.head().to_dict(orient='records'))
    # 转换为记录列表（list[dict]）
    data_to_insert = df.to_dict(orient='records')  # 关键：使用 orient='records'

    # 插入数据库
    engine = RDB_POOLS["pg_demo"]._engine
    metadata = MetaData()
    dm_fop_dimension_fcst_t = Table("dm_fop_dimension_fcst_t", metadata, autoload_with=engine)
    session.execute(
        dm_fop_dimension_fcst_t.insert(),
        data_to_insert
    )
    session.commit()


save_to_db(df)