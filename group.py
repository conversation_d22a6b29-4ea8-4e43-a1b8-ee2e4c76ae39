"""
时序预测分组处理模块

该模块用于处理时序数据的预测任务，包括：
1. 数据库连接和配置管理
2. 时序数据预处理
3. 多维度预测处理
4. 结果整合和保存

"""

import json
import logging
import os
import time
import warnings
from datetime import datetime
from multiprocessing import Pool, cpu_count
from typing import Any, Dict, List, Tuple, Union

import numpy as np
import pandas as pd
from sqlalchemy import MetaData, Table, text

from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline
from pyxis.utils.config_loader import ConfigLoader

RESULT_CSV_PATH = './zbx/dimension_group_pred_df_20250621_1043.csv'


# 配置警告和日志
warnings.filterwarnings("ignore")
# logging.basicConfig(level=logging.ERROR)

class EnvironmentConfig:
    """环境配置管理类"""

    @staticmethod
    def setup_environment():
        """设置环境变量"""
        env_vars = {
            'MAIN_KEY': '2a36e0e680f441388c4431dab24b11ab',
            'ASSIST_KEY': '50a5a01e7ec54576b62f69e96883b33a',
            'ENV': 'PROD-AIF',
            'J2C_SALT_ONE': '2,3,69,85,100,21,33,76,12,17,63,25,53,69,8,69',
            'PYTHONUNBUFFERED': '1',
            'WORK_KEY_CIPHER': '20~A2~9CAC7B32C55148437335E1619CB2C3368134E1CABA25C2B6A1EA4CD7BF1BF244~EAA3C29C1F0C77277EF232BD8AEF5D5076CD5A938A2300388040E9D883A12F5A09477B2A20003B86F53BE5AA30D943B2~E693B3E6FCBB6C8F490589C1F8F4A21EAC678DCBF6DA434BD756106D8C68C1CA',
            'STATIC_TOKEN': '20~A2~7113DEBEE14EF53C322CE0A864C21099EC2FFC5619A4A791E8F5482BB8C1B60E~A378B72456F3F8F8C34214D6A759089613FF8DBF3CA6A69C733E8C6471D7853A8E33FFB0BE71B9C7D071EC436C2DC96AB7999AE9E65D293F~E5BE4BC473C03D7AD54632C447D7B8BE4453EB8E17AD0935758770358B5AF118'
        }

        for key, value in env_vars.items():
            os.environ[key] = value

    @staticmethod
    def load_config() -> dict:
        """加载配置文件"""
        config_path = os.path.join("src/config", f"{os.environ.get('ENV', 'PROD-AIF')}.json")
        try:
            with open(config_path, "r", encoding="utf8") as file:
                return json.load(file)
        except FileNotFoundError:
            logging.error(f"配置文件未找到: {config_path}")
            raise
        except json.JSONDecodeError:
            logging.error(f"配置文件格式错误: {config_path}")
            raise

EnvironmentConfig.setup_environment()
CONFIG_LOADER = ConfigLoader(EnvironmentConfig.load_config())
LOGGER = CONFIG_LOADER.get_logger()

class DatabaseManager:
    """数据库管理类"""

    def __init__(self):
        self.config_loader = None
        self.rdb_pools = None
        self.session = None
        self._initialize()

    def _initialize(self):
        """初始化数据库连接"""
        try:

            self.config_loader = CONFIG_LOADER
            self.rdb_pools = self.config_loader.get_rdb_pools()
            self._startup()
            self.session = next(self.rdb_pools["pg_demo"].get_session_with_commit())
            LOGGER.info("数据库连接初始化成功")
        except Exception as e:
            LOGGER.error(f"数据库初始化失败: {str(e)}")
            raise

    def _startup(self):
        """启动数据库连接池"""
        for pool in self.rdb_pools.values():
            pool.reflect()

    def shutdown(self):
        """关闭数据库连接"""
        if self.rdb_pools:
            for pool in self.rdb_pools.values():
                pool.release()
            LOGGER.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()
class DataConfig:
    """数据配置类，包含维度定义和预测参数"""

    # 维度定义
    DIMENSION_SUBCATEGORY_DIM = [
        'scenarios', 'bg_code', 'bg_name', 'oversea_code', 'oversea_desc',
        'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name', 'currency',
        'dimension_subcategory_code', 'dimension_subcategory_cn_name', 'dimension_subcategory_en_name'
    ]

    DIMENSION_GROUP_DIM = [
        'scenarios', 'bg_code', 'bg_name', 'oversea_code', 'oversea_desc',
        'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name', 'currency',
        'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name'
    ]

    LV2_DIM = [
        'scenarios', 'bg_code', 'bg_name', 'oversea_code', 'oversea_desc',
        'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name'
    ]

    # 预测因子定义
    DIMENSION_PRED_LIST = ['unit_cost', 'unit_price', 'carryover_rate']
    NONDIMENSION_PRED_LIST = ['rev_percent', 'mgp_ratio']
    LV2_WITHDIM_PRED_LIST = ['mgp_adjust_ratio', 'mca_adjust_ratio']
    LV2_NONDIM_PRED_LIST = ['mgp_ratio_after', 'mgp_adjust_ratio', 'mca_adjust_ratio', 'equip_rev_cons_after_amt']

    # 需要区间预测的指标
    INDICATORS_NEED_INTERVAL_PREDICTION = ['unit_cost', 'unit_price', 'mgp_ratio', 'equip_rev_cons_after_amt']

    @staticmethod
    def get_prediction_params(steps: int = 6) -> dict:
        """获取预测参数配置"""
        return {
            'anomaly_entry_conditions': 'bayes_cycle_test',  # 打开异常检测
            'anomaly_model_name': 'three_sigma',  # 异常检测方法
            'anomaly_kwargs': {
                'thold': 4,
                'post_process_method': 'ignore_anomaly_by_month',
                'ignore_month_list': [11, 12]
            },  # 异常检测参数
            'reference_detection': True,  # 参考检测
            'line_trend_thres': 0.8,  # 线性外推判断阈值
            'adjust_tag': False,  # 调整预测结果：保持周期内递增
            'bayes_p_value': 0.01,  # 变点检测参数
            'is_truncated': True,  # 是否使用变点后的数据
            'ignore_last_n': 13,  # 忽略末尾最后几个时点的变点
            'short_ts_threshold': 6,  # 短时序阈值
            'fixed_test_size': 6,  # 测试集长度
            'models': ['prophet', 'auto_ets', 'naive'],  # 预测模型
            'eval_metric': 'mae',  # 评估函数
            'confidence': 0.95,  # 区间预测置信度
            'ensemble_method': 'weight',  # 模型融合方法
            'plot': False,  # 是否画图
            'model_params': {
                'arima': {'seasonal': True},
                'auto_ets': {
                    'seasonal_periods': 12,
                    'params': {'yearly_accumulation': True}
                },
                'prophet': {
                    'add_seasonality': {
                        'name': 'yearly',
                        'period': 365,
                        'fourier_order': 12
                    }
                }
            },  # 时序预测模型参数
            'steps': steps,  # 预测步长
            'predict_rule': 'fill_data',  # 规则预测方法
            'ts_name': 'Data'
        }


class DataLoader:
    """数据加载类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.session = db_manager.session

    def load_historical_data(self) -> pd.DataFrame:
        """加载历史数据"""
        try:
            # 回滚之前的事务（如果有错误）
            self.session.rollback()

            sql = """
            SELECT *
            FROM dm_fop_dimension_tgt_period_filled_t
            WHERE LEFT(target_period, 6) < '202407'
              AND del_flag = 'N'
              AND currency = 'CNY'
              AND bg_name IN ('运营商', '政企')
            """

            LOGGER.info("开始加载历史数据")
            results = self.session.execute(text(sql))
            columns = results.cursor.description
            rows = results.fetchall()

            # 转换为DataFrame
            results_with_columns = [dict(zip([col[0] for col in columns], row)) for row in rows]
            df = pd.DataFrame(results_with_columns)

            LOGGER.info(f"成功加载 {len(df)} 条历史数据记录")
            return df

        except Exception as e:
            LOGGER.error(f"加载历史数据失败: {str(e)}")
            raise

    def calculate_prediction_steps(self, df: pd.DataFrame) -> int:
        """计算预测步长"""
        try:
            max_period_id = df['period_id'].max()
            current_year = int(str(max_period_id)[:4])
            current_month = int(str(max_period_id)[4:6])
            target_month = 12
            steps = 12 + (target_month - current_month) + 1

            LOGGER.info(f"从当前期到第二年12月的月份个数为：{steps}")
            return steps

        except Exception as e:
            LOGGER.error(f"计算预测步长失败: {str(e)}")
            return 6  # 默认值
class DataValidator:
    """数据验证类"""

    @staticmethod
    def validate_data_quality(df: pd.DataFrame, cols_to_check: List[str]) -> None:
        """验证数据质量，检查空值情况"""
        try:
            null_stats = pd.DataFrame({
                '空值数量': df[cols_to_check].isna().sum(),
                '空值占比': df[cols_to_check].isna().mean().round(4) * 100
            })

            if not null_stats.empty and null_stats['空值数量'].sum() > 0:
                LOGGER.warning(f"数据质量检查 - 发现空值:\n{null_stats.to_string()}")
            else:
                LOGGER.info("数据质量检查通过 - 无空值")

        except Exception as e:
            LOGGER.error(f"数据质量验证失败: {str(e)}")

    @staticmethod
    def preprocess_ytd_data(df: pd.DataFrame) -> pd.DataFrame:
        """预处理YTD数据，填充空值"""
        try:
            # 找到所有以_ytd_data结尾的列
            ytd_cols = list(df.filter(regex='_ytd_data$').columns)

            if ytd_cols:
                # 将YTD数据的空值填充为1
                df[ytd_cols] = df[ytd_cols].fillna(1)
                LOGGER.info(f"已处理 {len(ytd_cols)} 个YTD数据列的空值")

            return df

        except Exception as e:
            LOGGER.error(f"YTD数据预处理失败: {str(e)}")
            return df


class TimeSeriesProcessor:
    """时序数据处理类"""

    def __init__(self, prediction_params: dict):
        self.prediction_params = prediction_params

    def process_single_series(
        self,
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: Union[None, str] = None
    ) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
        """
        处理单个时序数据

        Args:
            df: 输入数据框
            period_col: 时间列名
            data_col: 数据列名
            image_name: 图像名称标识

        Returns:
            预测结果的五元组：(预测值, 区间预测, 分布, 中间数据, 消息)
        """
        try:
            # 检查数据是否为空
            if df.empty:
                LOGGER.warning(f"数据为空: {image_name}")
                empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
                return empty_df, empty_df, np.array([]), {}, "Missing dimensions"

            # 数据预处理
            processed_df = self._preprocess_data(df.copy(), period_col, data_col, image_name)

            # 检查是否有YTD数据
            ytd_col = data_col + '_ytd_data'
            if ytd_col in processed_df.columns:
                processed_df['ytd_data'] = processed_df[ytd_col]
                final_df = processed_df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
            else:
                final_df = processed_df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)

            # 调用预测管道
            return rule_profit_pipeline(final_df, self.prediction_params)

        except Exception as e:
            LOGGER.error(f"处理时序数据失败 {image_name}: {str(e)}")
            empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
            return empty_df, empty_df, np.array([]), {}, f"Error: {str(e)}"

    def _preprocess_data(
        self,
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: str
    ) -> pd.DataFrame:
        """预处理时序数据"""
        # 处理时间格式
        df['Time'] = df[period_col].apply(self._format_time_column)
        df['UNION_ATTR'] = image_name
        df['Data'] = df[data_col]

        # 排序并处理空值
        df.sort_values(by='Time', inplace=True)
        df['Data'] = df['Data'].fillna(method='ffill')  # 向前填充
        df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
        df['Data'] = df['Data'].fillna(0)  # 剩余空值置0

        return df

    @staticmethod
    def _format_time_column(x) -> str:
        """格式化时间列"""
        x_str = str(x)
        if x_str.endswith('YTD'):
            return f"{x_str[:4]}-{x_str[4:6]}"
        else:
            return x_str[:-3] if len(x_str) > 3 else x_str


class PredictionResultIntegrator:
    """预测结果整合类"""

    def __init__(self, ts_processor: TimeSeriesProcessor):
        self.ts_processor = ts_processor

    def integrate_results(
        self,
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        use_multiprocessing: bool = True
    ) -> pd.DataFrame:
        """
        整合所有预测结果

        Args:
            his_df: 历史数据
            dim: 维度列表
            pred_cols: 预测列列表
            period_col: 时间列名
            use_multiprocessing: 是否使用多进程

        Returns:
            整合后的预测结果DataFrame
        """
        try:
            LOGGER.info("开始整合预测结果")

            # 创建维度分组字典
            dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}

            # 提取唯一的维度组合并过滤NaN
            pred_df = his_df[dim].drop_duplicates()
            pred_df = pred_df.dropna(subset=[dim[-1]])

            total_tasks = len(pred_df)
            LOGGER.info(f"发现 {total_tasks} 个唯一维度组合需要处理")

            if use_multiprocessing and total_tasks > 1:
                return self._process_with_multiprocessing(pred_df, dim, pred_cols, period_col, dim_dict)
            else:
                return self._process_sequentially(pred_df, dim, pred_cols, period_col, dim_dict)

        except Exception as e:
            LOGGER.error(f"整合预测结果失败: {str(e)}")
            raise

    def _process_sequentially(
        self,
        pred_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        dim_dict: Dict
    ) -> pd.DataFrame:
        """顺序处理预测任务"""
        result_dfs = []

        for idx, row in pred_df.iterrows():
            dim_key = tuple(row[d] for d in dim)
            sub_df = dim_dict.get(dim_key, pd.DataFrame())

            result_rows = self._process_single_dimension(
                dim_key, dim, pred_cols, period_col, sub_df
            )
            result_dfs.extend(result_rows)

        return pd.DataFrame(result_dfs)

    def _process_with_multiprocessing(
        self,
        pred_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        dim_dict: Dict
    ) -> pd.DataFrame:
        """使用多进程处理预测任务"""
        # 准备任务参数
        tasks = []
        for _, row in pred_df.iterrows():
            dim_key = tuple(row[d] for d in dim)
            sub_df = dim_dict.get(dim_key, pd.DataFrame())
            tasks.append((dim_key, dim, pred_cols, period_col, sub_df))

        # 设置进程数
        processes = min(cpu_count(), len(tasks))
        LOGGER.info(f"使用 {processes} 个进程进行并行处理")

        start_time = time.time()
        with Pool(processes=processes) as pool:
            results = pool.starmap(self._process_single_dimension, tasks)

        duration = time.time() - start_time
        LOGGER.info(f"并行处理完成，耗时 {duration:.2f}s")

        # 合并结果
        main_result = pd.DataFrame([item for sublist in results for item in sublist])
        LOGGER.info(f"最终结果包含 {len(main_result)} 行")

        return main_result


    def _process_single_dimension(
        self,
        dim_key: Tuple[Any],
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        sub_df: pd.DataFrame
    ) -> List[Dict[str, Any]]:
        """
        处理单个维度组合的所有预测列

        Args:
            dim_key: 维度键值元组
            dim: 维度列表
            pred_cols: 预测列列表
            period_col: 时间列名
            sub_df: 子数据框

        Returns:
            处理结果行列表
        """
        LOGGER.debug(f"开始处理维度: {dim_key}")
        start_time = time.time()

        result_rows = []
        result_row = {d: v for d, v in zip(dim, dim_key)}

        for pred_col in pred_cols:
            try:
                # 调用时序处理器进行预测
                pred, pi_pred, pi_dist, mid_data, msg = self.ts_processor.process_single_series(
                    sub_df,
                    period_col=period_col,
                    data_col=pred_col,
                    image_name='-'.join(map(str, dim_key)) + pred_col
                )

                # 提取中间数据
                pi_confidence = mid_data.get('pi_confidence', None)
                eval_result = mid_data.get('eval_result', None)

                # 重置索引并合并预测结果
                pred = pred.reset_index()
                pi_pred = pi_pred.reset_index()
                pred_ts = pred.merge(pi_pred, on='Time', how='left')

                # 根据是否需要区间预测来处理结果
                if pred_col in DataConfig.INDICATORS_NEED_INTERVAL_PREDICTION:
                    result_rows.extend(
                        self._create_interval_prediction_rows(
                            result_row, pred_ts, pred_col, period_col, pi_dist, pi_confidence, eval_result, msg
                        )
                    )
                else:
                    result_rows.extend(
                        self._create_point_prediction_rows(
                            result_row, pred_ts, pred_col, period_col, pi_dist, msg
                        )
                    )

            except Exception as e:
                LOGGER.error(f"处理预测列 {pred_col} 失败 (维度: {dim_key}): {str(e)}")
                # 添加错误标记行
                error_row = result_row.copy()
                error_row.update({
                    period_col: None,
                    f"{pred_col}_fcst": None,
                    f"{pred_col}_fcst_upper": None,
                    f"{pred_col}_fcst_lower": None,
                    f"{pred_col}_pi_dist": None,
                    f"{pred_col}_fcst_conf": None,
                    f"{pred_col}_fcst_acc": None,
                    'msg': f"Error: {str(e)}"
                })
                result_rows.append(error_row)

        duration = time.time() - start_time
        LOGGER.debug(f"完成处理维度 {dim_key}，耗时 {duration:.2f}s，生成 {len(result_rows)} 行")
        return result_rows

    def _create_interval_prediction_rows(
        self,
        result_row: Dict,
        pred_ts: pd.DataFrame,
        pred_col: str,
        period_col: str,
        pi_dist: np.ndarray,
        pi_confidence: Any,
        eval_result: Any,
        msg: str
    ) -> List[Dict]:
        """创建区间预测结果行"""
        rows = []

        if not pred_ts.empty:
            for _, time_row in pred_ts.iterrows():
                temp = result_row.copy()
                temp.update({
                    period_col: time_row['Time'],
                    f"{pred_col}_fcst": time_row.get('yhat'),
                    f"{pred_col}_fcst_upper": time_row.get('yhat_upper'),
                    f"{pred_col}_fcst_lower": time_row.get('yhat_lower'),
                    f"{pred_col}_pi_dist": pi_dist,
                    f"{pred_col}_fcst_conf": pi_confidence,
                    f"{pred_col}_fcst_acc": eval_result,
                    'msg': msg
                })
                rows.append(temp)
        else:
            # 空数据情况
            temp = result_row.copy()
            temp.update({
                period_col: None,
                f"{pred_col}_fcst": None,
                f"{pred_col}_fcst_upper": None,
                f"{pred_col}_fcst_lower": None,
                f"{pred_col}_pi_dist": None,
                f"{pred_col}_fcst_conf": None,
                f"{pred_col}_fcst_acc": None,
                'msg': None
            })
            rows.append(temp)

        return rows

    def _create_point_prediction_rows(
        self,
        result_row: Dict,
        pred_ts: pd.DataFrame,
        pred_col: str,
        period_col: str,
        pi_dist: np.ndarray,
        msg: str
    ) -> List[Dict]:
        """创建点预测结果行"""
        rows = []

        if not pred_ts.empty:
            for _, time_row in pred_ts.iterrows():
                temp = result_row.copy()
                temp.update({
                    period_col: time_row['Time'],
                    f"{pred_col}_fcst": time_row.get('yhat'),
                    f"{pred_col}_pi_dist": pi_dist,
                    'msg': msg
                })
                rows.append(temp)
        else:
            # 空数据情况
            temp = result_row.copy()
            temp.update({
                period_col: None,
                f"{pred_col}_fcst": None,
                f"{pred_col}_pi_dist": None,
                'msg': None
            })
            rows.append(temp)

        return rows


class PredictionPipeline:
    """预测管道主类"""

    def __init__(self):
        self.db_manager = db_manager
        self.data_loader = DataLoader(self.db_manager)
        self.data_validator = DataValidator()

        # 初始化预测参数
        self.prediction_params = None
        self.ts_processor = None
        self.result_integrator = None

    def run_prediction_pipeline(self) -> pd.DataFrame:
        """运行完整的预测管道"""
        try:
            LOGGER.info("开始运行预测管道")

            # 1. 加载历史数据
            df = self.data_loader.load_historical_data()

            # 2. 计算预测步长并设置参数
            steps = self.data_loader.calculate_prediction_steps(df)
            self.prediction_params = DataConfig.get_prediction_params(steps)

            # 3. 初始化处理器
            self.ts_processor = TimeSeriesProcessor(self.prediction_params)
            self.result_integrator = PredictionResultIntegrator(self.ts_processor)

            # 4. 数据质量验证
            cols_to_check = DataConfig.DIMENSION_PRED_LIST + DataConfig.NONDIMENSION_PRED_LIST
            self.data_validator.validate_data_quality(df, cols_to_check)

            # 5. 预处理YTD数据
            df = self.data_validator.preprocess_ytd_data(df)

            # 6. 执行量纲分组预测
            dimension_group_result = self._process_dimension_group_prediction(df)

            # 7. 聚合结果
            final_result = self._aggregate_prediction_results(dimension_group_result)

            # 8. 保存结果
            self._save_results(final_result)

            LOGGER.info("预测管道运行完成")
            return final_result

        except Exception as e:
            LOGGER.error(f"预测管道运行失败: {str(e)}")
            raise

    def _process_dimension_group_prediction(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理量纲分组预测"""
        try:
            LOGGER.info("开始处理量纲分组预测")

            # 筛选量纲分组数据
            dimension_group_df = df[
                (df['scenarios'] == '量纲分组') &
                (df['dimension_group_code'] != 'NODIM')
            ]

            if dimension_group_df.empty:
                LOGGER.warning("量纲分组数据为空")
                return pd.DataFrame()

            # 执行预测
            result = self.result_integrator.integrate_results(
                his_df=dimension_group_df,
                dim=DataConfig.DIMENSION_GROUP_DIM,
                pred_cols=DataConfig.DIMENSION_PRED_LIST,
                period_col='target_period'
            )

            LOGGER.info(f"量纲分组预测完成，生成 {len(result)} 条记录")
            return result

        except Exception as e:
            LOGGER.error(f"量纲分组预测失败: {str(e)}")
            raise

    def _aggregate_prediction_results(self, pred_df: pd.DataFrame) -> pd.DataFrame:
        """聚合预测结果"""
        try:
            if pred_df.empty:
                return pd.DataFrame()

            LOGGER.info("开始聚合预测结果")

            # 定义聚合维度
            group_cols = [
                'scenarios', 'bg_code', 'bg_name', 'target_period',
                'oversea_code', 'oversea_desc', 'lv1_code', 'lv1_name',
                'lv2_code', 'lv2_name', 'currency', 'dimension_group_code',
                'dimension_group_cn_name', 'dimension_group_en_name'
            ]

            # 定义聚合规则
            agg_rules = {
                'unit_cost_fcst': 'max',
                'carryover_rate_fcst': 'max',
                'unit_price_fcst': 'max'
            }

            # 执行聚合
            result = pred_df.groupby(group_cols).agg(agg_rules).reset_index()

            LOGGER.info(f"聚合完成，最终结果 {len(result)} 条记录")
            return result

        except Exception as e:
            LOGGER.error(f"聚合预测结果失败: {str(e)}")
            raise

    def _save_results(self, df: pd.DataFrame):
        """保存结果到CSV文件"""
        try:
            output_file = RESULT_CSV_PATH
            df.to_csv(output_file, index=False)
            LOGGER.info(f"结果已保存到 {output_file}")

        except Exception as e:
            LOGGER.error(f"保存结果失败: {str(e)}")
            raise

class DatabaseSaver:
    """数据库保存类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.session = db_manager.session
        self.engine = db_manager.rdb_pools["pg_demo"]._engine

    def save_predictions_to_database(self, df: pd.DataFrame) -> None:
        """保存预测结果到数据库"""
        try:
            if df.empty:
                LOGGER.warning("预测结果为空，跳过数据库保存")
                return

            LOGGER.info("开始保存预测结果到数据库")

            # 数据预处理
            processed_df = self._preprocess_for_database(df.copy())

            # 转换为记录列表
            data_to_insert = processed_df.to_dict(orient='records')

            # 插入数据库
            self._insert_to_database(data_to_insert)

            LOGGER.info(f"成功保存 {len(data_to_insert)} 条记录到数据库")

        except Exception as e:
            LOGGER.error(f"保存到数据库失败: {str(e)}")
            self.session.rollback()
            raise

    def _preprocess_for_database(self, df: pd.DataFrame) -> pd.DataFrame:
        """为数据库保存预处理数据"""
        try:
            # 重命名列
            df = self._rename_columns_for_database(df)

            # 选择需要保存的列
            required_cols = self._get_required_database_columns()
            available_cols = list(set(required_cols) & set(df.columns))
            df = df[available_cols]

            # 添加元数据列
            df = self._add_metadata_columns(df)

            # 处理数据类型
            df = self._process_data_types(df)

            return df

        except Exception as e:
            LOGGER.error(f"数据预处理失败: {str(e)}")
            raise

    def _rename_columns_for_database(self, df: pd.DataFrame) -> pd.DataFrame:
        """重命名列以匹配数据库表结构"""
        rename_mapping = {
            # 处理比率相关列名
            'mgp_ratio_fcst': 'mgp_rate_before_fcst',
            'mgp_ratio_fcst_upper': 'mgp_rate_before_fcst_upper',
            'mgp_ratio_fcst_lower': 'mgp_rate_before_fcst_lower',
            'mgp_ratio_fcst_conf': 'mgp_rate_before_fcst_conf',
            # 处理结转率列名
            'carryover_rate_fcst': 'carryover_ratio_fcst'
        }

        # 应用重命名
        for old_col, new_col in rename_mapping.items():
            if old_col in df.columns:
                df.rename(columns={old_col: new_col}, inplace=True)

        return df


    def _get_required_database_columns(self) -> List[str]:
        """获取数据库需要的列"""
        return [
            'period_id', 'time_window_code', 'fcst_type', 'scenarios',
            'bg_code', 'bg_name', 'oversea_code', 'oversea_desc',
            'lv1_code', 'lv1_name', 'lv2_code', 'lv2_name', 'currency',
            'dimension_group_code', 'dimension_group_cn_name', 'dimension_group_en_name',
            'dimension_subcategory_code', 'dimension_subcategory_cn_name', 'dimension_subcategory_en_name',
            'target_period', 'rev_percent_fcst', 'unit_price_fcst_conf',
            'unit_price_fcst', 'unit_price_fcst_upper', 'unit_price_fcst_lower',
            'unit_cost_fcst_conf', 'unit_cost_fcst', 'unit_cost_fcst_upper', 'unit_cost_fcst_lower',
            'mgp_rate_before_fcst_conf', 'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper',
            'mgp_rate_before_fcst_lower', 'unit_price_fcst_acc', 'unit_cost_fcst_acc',
            'carryover_ratio_fcst', 'remark', 'del_flag'
        ]

    def _add_metadata_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加元数据列"""
        # 添加固定值列
        df['period_id'] = 202506
        df['fcst_type'] = 'YTD法'
        df['remark'] = '06201424'
        df['del_flag'] = 'N'

        # 格式化目标期间
        if 'target_period' in df.columns:
            df['target_period'] = pd.to_datetime(df['target_period']).dt.strftime("%Y%m")

        return df

    def _process_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理数据类型"""
        # 需要转换为数值类型的列
        numeric_cols = [
            'rev_percent_fcst', 'unit_price_fcst_conf', 'unit_price_fcst',
            'unit_price_fcst_upper', 'unit_price_fcst_lower', 'unit_cost_fcst_conf',
            'unit_cost_fcst', 'unit_cost_fcst_upper', 'unit_cost_fcst_lower',
            'mgp_rate_before_fcst_conf', 'mgp_rate_before_fcst', 'mgp_rate_before_fcst_upper',
            'mgp_rate_before_fcst_lower', 'unit_price_fcst_acc', 'unit_cost_fcst_acc',
            'carryover_ratio_fcst'
        ]

        for col in numeric_cols:
            if col in df.columns:
                # 转换为数值类型，错误值转为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')
                # 将NaN替换为None（数据库NULL）
                df[col] = df[col].astype(float).replace({np.nan: None})

        return df

    def _insert_to_database(self, data_to_insert: List[Dict]) -> None:
        """插入数据到数据库"""
        try:
            # 创建表元数据
            metadata = MetaData()
            dm_fop_dimension_fcst_t = Table(
                "dm_fop_dimension_fcst_t",
                metadata,
                autoload_with=self.engine
            )

            # 执行插入
            self.session.execute(
                dm_fop_dimension_fcst_t.insert(),
                data_to_insert
            )
            self.session.commit()

        except Exception as e:
            LOGGER.error(f"数据库插入失败: {str(e)}")
            self.session.rollback()
            raise


def main():
    """主函数"""
    try:
        # 创建预测管道并运行
        pipeline = PredictionPipeline()
        result_df = pipeline.run_prediction_pipeline()

        # 保存到数据库
        # db_saver = DatabaseSaver(db_manager)
        # db_saver.save_predictions_to_database(result_df)

        LOGGER.info("预测任务完成")

    except Exception as e:
        LOGGER.error(f"主程序执行失败: {str(e)}")
        raise
    finally:
        # 清理资源
        db_manager.shutdown()


if __name__ == "__main__":
    main()