import base64
import json

import requests
from cachetools.func import ttl_cache
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_not_exception_type

# IAM Token URLs
PROD3_IAM_TOKEN_URL = "https://iam.his-op.huawei.com/iam/auth/token"
PROD2_IAM_TOKEN_URL = "https://iam.heds.huawei.com/iam/auth/token"
BETA2_IAM_TOKEN_URL = "https://iam-icsl.heds-beta2.huawei.com/iam/auth/token"
BETA3_IAM_TOKEN_URL = "https://iam.his-op-beta.huawei.com/iam/auth/token"


class HisAuthorizationError(Exception):
    def __init__(self):
        super().__init__()


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_dynamic_token(url: str, app_id: str, static_token: str) -> str:
    info = {
        "appId": app_id,
        "credential": str(base64.b64encode(bytes(static_token, "utf-8")), "utf-8")
    }

    response = requests.post(
        url,
        json=info, headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisAuthorizationError()
    result = json.loads(response.text)
    dynamic_token = result["result"]
    if dynamic_token is None:
        raise HisAuthorizationError()
    return dynamic_token


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_iam_token(enterprise: str, project: str, account: str, secret: str, env: str) -> str:
    """
    获取iam动态token
    :param enterprise: 企业id
    :param project: 项目id
    :param account: 账号
    :param secret: 秘钥
    :param env: 环境 prod3/prod2/beta3/beta2分别对应3.0生产/2.0生产/3.0测试/2.0测试
    :return: dynamic token
    """
    if env == 'prod2':  # 蓝版2.0生产
        token_api = PROD2_IAM_TOKEN_URL
    elif env == 'prod3':  # 蓝版3.0生产:
        token_api = PROD3_IAM_TOKEN_URL
    elif env == "beta2":  # 蓝版2.0测试
        token_api = BETA2_IAM_TOKEN_URL
    else:  # 蓝版3.0测试
        token_api = BETA3_IAM_TOKEN_URL

    try:
        data = {
            "data": {"type": "JWT-Token",
                     "attributes": {
                         "method": "CREATE",
                         "account": account,
                         "secret": secret,
                         "project": project,
                         "enterprise": enterprise
                     }
                     }
        }
        response = requests.post(url=token_api, json=data,
                                 headers={'Content-Type': 'application/json'},
                                 verify=False,
                                 timeout=60)

        if response.status_code != 200:
            raise HisAuthorizationError()

        result = response.json()
        token = result.get("access_token")
        if not token:
            raise HisAuthorizationError()
        return token
    except Exception as e:
        if isinstance(e, HisAuthorizationError):
            raise
        raise HisAuthorizationError() from e
