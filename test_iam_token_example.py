#!/usr/bin/env python3
"""
测试 IAM token 功能的示例代码
"""

from pyxis.utils.authorization.his_authorization import get_iam_token

def test_iam_token():
    """测试 IAM token 获取功能"""
    # 示例参数（请根据实际情况修改）
    enterprise = "8747b9ef89c71792018a4533d8be06c1"  # 企业id
    project = "904eef23d0874c1da63b14d6b1c9e946"     # 项目id
    account = "904eef23d0874c1da63b14d6b1c9e946"     # 账号
    secret = "i4rG1qJtKlgqf7T79M84kU3GVX1o7z/8544/VC3o"  # 秘钥
    env = "prod3"  # 环境 prod3/prod2/beta3/beta2分别对应3.0生产/2.0生产/3.0测试/2.0测试
    
    try:
        dynamic_token = get_iam_token(enterprise, project, account, secret, env)
        print(f"成功获取 IAM token: {dynamic_token}")
        return dynamic_token
    except Exception as e:
        print(f"获取 IAM token 失败: {e}")
        return None

if __name__ == "__main__":
    test_iam_token()
